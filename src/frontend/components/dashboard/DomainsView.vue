<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useDomainApi } from '@composables/useApi'
import { useConfirm } from '@composables/useConfirm'
import { useToast } from '@composables/useToast'
import { useMetrics } from '@composables/useMetrics'
import { useOnboarding } from '@composables/useOnboarding'
import DomainsTable from './DomainsTable.vue'
import TestEmailCard from '../onboarding/TestEmailCard.vue'
import OnboardingGuide from '../onboarding/OnboardingGuide.vue'

// Router
const router = useRouter()
const route = useRoute()

// Composables
const { deleteDomain } = useDomainApi()
const { confirmDelete } = useConfirm()
const { success, error } = useToast()
const { refreshMetrics } = useMetrics()
const { shouldShowOnboarding } = useOnboarding()

// State for domains data
const domains = ref([])
const isLoading = ref(true)
const userId = ref<string>('')

// Data refresh system
const { refreshState, updateData, triggerRefresh } = useDataRefresh()

// Load user info
const loadUserInfo = async () => {
  try {
    const response = await fetch('/api/auth/check', {
      credentials: 'include'
    })

    if (response.ok) {
      const data = await response.json()
      userId.value = data.user.id
    }
  } catch (error) {
    console.error('Failed to load user info:', error)
  }
}

// Load domains data
const loadDomains = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []

    // Update global data store
    updateData('domains', data.domains || [])
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.domains, () => {
  loadDomains()
})

// Handle view logs navigation
const handleViewLogs = (domainId: string) => {
  // Navigate to logs tab and store domain selection for LogsView to pick up
  sessionStorage.setItem('logsView_selectedDomain', domainId)
  router.push('/logs')
}

// Handle domain editing
const handleEditDomain = (domain: any) => {
  // Extract configuration from the domain object
  const config = domain.configuration || {}

  const modalData = {
    id: domain.id,
    domain: domain.domain || domain.domainName, // Use actual domain name
    webhookId: domain.webhook?.id || domain.webhookId, // Extract webhook ID from webhook object or direct property
    allowAttachments: config.allowAttachments || false,
    includeEnvelope: config.includeEnvelope !== false, // Default to true if not explicitly set
    onSuccess: () => {
      loadDomains() // Refresh the domains list
      triggerRefresh('domains')
    }
  }

  // Open the edit modal
  ;(window as any).openModal('edit-domain', modalData)
}

// Handle domain deletion
const handleDeleteDomain = async (domainId: string, domainName: string) => {
  try {
    const confirmed = await confirmDelete(domainName, 'domain')
    if (!confirmed) return

    await deleteDomain(domainId)
    success(`Domain "${domainName}" deleted successfully`)

    // Refresh the domains list and trigger metrics update
    await loadDomains()
    triggerRefresh('domains')

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after domain deletion:', error)
      }
    }, 500)
  } catch (err) {
    console.error('Failed to delete domain:', err)
    error(err instanceof Error ? err.message : 'Failed to delete domain')
  }
}

onMounted(async () => {
  await Promise.all([
    loadUserInfo(),
    loadDomains()
  ])

  // Auto-launch logic removed - now handled directly in onboarding component
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadDomains
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Empty state with enhanced onboarding -->
    <div v-else-if="domains.length === 0" class="space-y-6">
      <OnboardingGuide :user-id="userId" />

      <!-- Fallback to simple test card if onboarding is dismissed -->
      <div v-if="!shouldShowOnboarding" class="space-y-6">
        <div class="text-center py-8">
          <div class="max-w-md mx-auto">
            <h3 class="text-lg font-semibold text-base-content mb-2">
              No domains configured yet
            </h3>
            <p class="text-base-content/70 mb-4">
              Setup your own domain to receive emails at custom aliases.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Domains table -->
    <DomainsTable
      v-else
      :domains="domains"
      @refresh="loadDomains"
      @edit-domain="handleEditDomain"
      @view-logs="handleViewLogs"
      @delete-domain="handleDeleteDomain"
    />
  </div>
</template>
