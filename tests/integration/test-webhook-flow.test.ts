import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestFastifyApp
} from '../setup/test-db-setup.js';

describe('Test Webhook Flow', () => {
  setupTestDatabase();

  let app: FastifyInstance;
  let testUser: any;
  let userIdSuffix: string;
  let testEmail: string;

  beforeAll(async () => {
    app = await createTestFastifyApp();

    // Register the email processing route
    await app.register(async function (fastify) {
      await fastify.register((await import('../../src/backend/routes/email.js')).emailRoutes, { prefix: '/api/email' });
    });

    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.email.deleteMany({
      where: { isTestWebhook: true }
    });

    // Create a test user
    testUser = await createTestUser({
      email: '<EMAIL>',
      password: 'password123'
    });

    // Get the last 8 characters of user ID
    userIdSuffix = testUser.id.slice(-8);
    testEmail = `${userIdSuffix}@test.emailconnect.eu`;
  });

  test('should process test webhook email successfully', async () => {
    // Create a sample raw email
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Test Email for Webhook
Date: ${new Date().toUTCString()}
Message-ID: <test-${Date.now()}@example.com>

This is a test email body for webhook processing.
It should be captured and stored as a test webhook.

Best regards,
Test Sender`;

    // Send email to processing endpoint
    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain',
        'X-Email-Source': 'test'
      }
    });

    // Should return success
    expect(response.statusCode).toBe(202);

    const responseBody = JSON.parse(response.body);
    expect(responseBody.success).toBe(true);
    expect(responseBody.messageId).toBeDefined();
    expect(responseBody.status).toBe('delivered');

    // Verify email was stored in database
    const storedEmail = await prisma.email.findUnique({
      where: { messageId: responseBody.messageId }
    });

    expect(storedEmail).toBeTruthy();
    expect(storedEmail!.isTestWebhook).toBe(true);
    expect(storedEmail!.deliveryStatus).toBe('DELIVERED');
    expect(storedEmail!.fromAddress).toBe('<EMAIL>');
    expect(storedEmail!.toAddresses).toEqual([testEmail]);
    expect(storedEmail!.subject).toBe('Test Email for Webhook');
    expect(storedEmail!.webhookPayload).toBeTruthy();
    expect(storedEmail!.domainId).toBeNull(); // Test webhooks don't belong to a domain
    expect(storedEmail!.deliveredAt).toBeTruthy();

    // Verify webhook payload structure
    const payload = storedEmail!.webhookPayload as any;
    expect(payload.message).toBeDefined();
    expect(payload.message.sender.email).toBe('<EMAIL>');
    expect(payload.message.recipient.email).toBe(testEmail);
    expect(payload.message.subject).toBe('Test Email for Webhook');
    expect(payload.message.content.text).toContain('This is a test email body');
    expect(payload.envelope).toBeDefined();
    expect(payload.envelope.messageId).toBe(responseBody.messageId);
  });

  test('should NOT increment user email usage for test webhooks', async () => {
    const initialUsage = testUser.currentMonthEmails;

    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Usage Test
Date: ${new Date().toUTCString()}
Message-ID: <usage-test-${Date.now()}@example.com>

Testing usage increment.`;

    await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    // Check that user's email usage was NOT incremented (test webhooks don't count)
    const updatedUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });

    expect(updatedUser!.currentMonthEmails).toBe(initialUsage); // Should remain the same
  });

  test('should reject test webhook if user not found', async () => {
    const fakeEmail = '<EMAIL>';
    
    const rawEmail = `From: <EMAIL>
To: ${fakeEmail}
Subject: Should Fail
Date: ${new Date().toUTCString()}
Message-ID: <fail-test-${Date.now()}@example.com>

This should fail.`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    // Should return 404 since no user matches the suffix
    expect(response.statusCode).toBe(404);
  });

  test('should handle malformed email addresses gracefully', async () => {
    const malformedEmail = '<EMAIL>'; // Only 7 chars before @
    
    const rawEmail = `From: <EMAIL>
To: ${malformedEmail}
Subject: Malformed Test
Date: ${new Date().toUTCString()}
Message-ID: <malformed-${Date.now()}@example.com>

This should be rejected.`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    // Should return 404 since suffix is wrong length
    expect(response.statusCode).toBe(404);
  });

  test('should respect user email limits for test webhooks', async () => {
    // Set user to be at their limit
    await prisma.user.update({
      where: { id: testUser.id },
      data: { 
        currentMonthEmails: testUser.monthlyEmailLimit,
        lastUsageReset: new Date() // Ensure we're in current month
      }
    });

    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Over Limit Test
Date: ${new Date().toUTCString()}
Message-ID: <limit-test-${Date.now()}@example.com>

This should be rejected due to limits.`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    // Should return 429 (Too Many Requests)
    expect(response.statusCode).toBe(429);
    
    const responseBody = JSON.parse(response.body);
    expect(responseBody.error).toBe('Too Many Requests');
    expect(responseBody.message).toContain('Monthly email limit exceeded');
  });

  test('should store complex email with attachments', async () => {
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Complex Email with Attachments
Date: ${new Date().toUTCString()}
Message-ID: <complex-${Date.now()}@example.com>
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain

This is the text body of the email.

--boundary123
Content-Type: text/html

<html><body><h1>HTML Body</h1><p>This is HTML content.</p></body></html>

--boundary123
Content-Type: application/pdf; name="document.pdf"
Content-Disposition: attachment; filename="document.pdf"
Content-Transfer-Encoding: base64

JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0K

--boundary123--`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    expect(response.statusCode).toBe(202);
    
    const responseBody = JSON.parse(response.body);
    const storedEmail = await prisma.email.findUnique({
      where: { messageId: responseBody.messageId }
    });

    const payload = storedEmail!.webhookPayload as any;
    
    // Verify complex content was parsed
    expect(payload.message.content.text).toContain('This is the text body');
    expect(payload.message.content.html).toContain('<h1>HTML Body</h1>');
    expect(payload.message.attachments).toBeDefined();
    expect(payload.message.attachments.length).toBeGreaterThan(0);
  });
});
