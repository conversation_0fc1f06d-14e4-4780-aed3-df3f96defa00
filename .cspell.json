{"version": "0.2", "language": "en", "words": ["emailconnect", "EmailConnect", "domainId", "aliasId", "webhookId", "domainName", "localPart", "destinationEmail", "webhookName", "webhookUrl", "allowAttachments", "includeEnvelope", "verificationStatus", "postfix", "xadi", "cmbw", "hopl", "uru", "qqyx", "nwc", "bru", "qf", "akd", "hopp", "qk", "cowjg", "gulpfile", "typecheck", "prebuild", "prepublishOnly", "devDependencies", "peerDependencies", "IExecuteFunctions", "ILoadOptionsFunctions", "INodeExecutionData", "INodePropertyOptions", "INodeType", "INodeTypeDescription", "NodeOperationError", "NodeConnectionType", "IHookFunctions", "IWebhookFunctions", "IWebhookResponseData", "emailConnectApiRequest", "getAll", "updateConfig", "getAliases", "getWebhooks", "getDomains", "getAliasesForDomain", "loadOptionsMethod", "getCurrentNodeParameter", "getNodeParameter", "getBodyData", "getNodeWebhookUrl", "getWorkflowStaticData", "checkExists", "webhookMethods", "noWebhookResponse", "workflowData", "verifyWebhook", "verificationToken", "previousWebhookId"], "ignorePaths": ["node_modules/**", "dist/**", "*.log", ".git/**", "package-lock.json", "*.min.js", "coverage/**"], "ignoreRegExpList": ["/\\b[A-Z0-9]{20,}\\b/g", "/\\b[a-f0-9]{24,}\\b/g", "/https?:\\/\\/[^\\s]+/g"]}