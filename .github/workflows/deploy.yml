name: CI/CD Pipeline for EmailConnect n8n Node

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run formatting check
        run: npm run format --check

      - name: Type checking
        run: npx tsc --noEmit

      - name: Security audit
        run: npm audit --audit-level moderate

      - name: Check for vulnerabilities
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Run security scan
        run: |
          npx audit-ci --config .audit-ci.json || echo "Security scan completed with warnings"

  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: quality
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test
        env:
          CI: true

      - name: Generate test coverage
        run: npm run test --coverage
        env:
          CI: true

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            coverage/
            junit.xml
          retention-days: 30

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  build:
    name: Build Package
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    outputs:
      version: ${{ steps.version.outputs.version }}
      should-release: ${{ steps.version.outputs.should-release }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build package
        run: npm run build

      - name: Check build output
        run: |
          echo "📦 Build completed successfully"
          ls -la dist/
          echo "✅ All required files generated"

      - name: Determine version and release
        id: version
        run: |
          # Check if this is a release commit (conventional commits)
          if git log -1 --pretty=%B | grep -E '^(feat|fix|perf|BREAKING CHANGE)'; then
            echo "should-release=true" >> $GITHUB_OUTPUT
            # Extract version from package.json
            VERSION=$(node -p "require('./package.json').version")
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "🚀 Release candidate detected: v$VERSION"
          else
            echo "should-release=false" >> $GITHUB_OUTPUT
            echo "📝 No release needed for this commit"
          fi

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            package.json
            README.md
            LICENSE
            CHANGELOG.md
          retention-days: 30

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' && needs.build.outputs.should-release == 'true'
    outputs:
      tag: ${{ steps.tag.outputs.tag }}
      release-id: ${{ steps.release.outputs.id }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from conventional commits
          if command -v conventional-changelog &> /dev/null; then
            conventional-changelog -p angular -i CHANGELOG.md -s
          else
            echo "📝 Generating basic changelog from git log..."
            echo "## [${{ needs.build.outputs.version }}] - $(date +%Y-%m-%d)" > TEMP_CHANGELOG.md
            echo "" >> TEMP_CHANGELOG.md
            git log --pretty=format:"- %s" --since="$(git describe --tags --abbrev=0 2>/dev/null || echo '1 week ago')" >> TEMP_CHANGELOG.md
            cat TEMP_CHANGELOG.md CHANGELOG.md > TEMP_FULL.md && mv TEMP_FULL.md CHANGELOG.md
          fi

      - name: Create and push tag
        id: tag
        run: |
          TAG="v${{ needs.build.outputs.version }}"
          echo "tag=$TAG" >> $GITHUB_OUTPUT
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag -a "$TAG" -m "Release $TAG"
          git push origin "$TAG"
          echo "🏷️ Created and pushed tag: $TAG"

      - name: Create GitHub Release
        id: release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.tag.outputs.tag }}
          release_name: Release ${{ steps.tag.outputs.tag }}
          body: |
            ## 🚀 EmailConnect n8n Node ${{ steps.tag.outputs.tag }}

            ### 📦 Installation
            ```bash
            npm install n8n-nodes-emailconnect
            ```

            ### 📋 Changes
            See [CHANGELOG.md](./CHANGELOG.md) for detailed changes.

            ### 🔗 Links
            - [npm package](https://www.npmjs.com/package/n8n-nodes-emailconnect)
            - [Documentation](https://github.com/xadi-hq/n8n-nodes-emailconnect#readme)
            - [EmailConnect Service](https://emailconnect.eu)
          draft: false
          prerelease: false

  publish:
    name: Publish to NPM
    runs-on: ubuntu-latest
    needs: [build, release]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' && needs.build.outputs.should-release == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Verify package contents
        run: |
          echo "📦 Verifying package contents..."
          ls -la dist/
          echo "✅ Package verification complete"

      - name: Publish to NPM
        run: |
          echo "🚀 Publishing to NPM..."
          npm publish --access public
          echo "✅ Package published successfully!"
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Update release with NPM link
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const releaseId = ${{ needs.release.outputs.release-id }};
            const version = "${{ needs.build.outputs.version }}";

            await github.rest.repos.updateRelease({
              owner,
              repo,
              release_id: releaseId,
              body: `## 🚀 EmailConnect n8n Node v${version}

              ### 📦 Installation
              \`\`\`bash
              npm install n8n-nodes-emailconnect@${version}
              \`\`\`

              ### 🔗 NPM Package
              📦 [View on NPM](https://www.npmjs.com/package/n8n-nodes-emailconnect/v/${version})

              ### 📋 Changes
              See [CHANGELOG.md](./CHANGELOG.md) for detailed changes.

              ### 🔗 Links
              - [Documentation](https://github.com/xadi-hq/n8n-nodes-emailconnect#readme)
              - [EmailConnect Service](https://emailconnect.eu)`
            });

      - name: Notify success
        run: |
          echo "🎉 Release ${{ needs.build.outputs.version }} published successfully!"
          echo "📦 NPM: https://www.npmjs.com/package/n8n-nodes-emailconnect"
          echo "🏷️ GitHub: https://github.com/xadi-hq/n8n-nodes-emailconnect/releases/tag/${{ needs.release.outputs.tag }}"
