{"ignorePatterns": [{"pattern": "^http://localhost"}, {"pattern": "^https://localhost"}, {"pattern": "^http://127.0.0.1"}, {"pattern": "^https://127.0.0.1"}], "replacementPatterns": [{"pattern": "^/", "replacement": "https://github.com/xadi-hq/n8n-nodes-emailconnect/blob/main/"}], "httpHeaders": [{"urls": ["https://github.com/"], "headers": {"Accept-Encoding": "zstd, br, gzip, deflate"}}], "timeout": "20s", "retryOn429": true, "retryCount": 3, "fallbackRetryDelay": "30s", "aliveStatusCodes": [200, 206]}